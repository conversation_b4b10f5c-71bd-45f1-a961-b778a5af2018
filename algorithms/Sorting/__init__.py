"""
Sorting Algorithms Scaling Analysis Package

This package contains scaling analysis implementations for various sorting algorithms,
measuring both time and space complexity across different input sizes.

Available Analyzers:
    - MergeSortScalingAnalyzer: Merge Sort algorithm analysis

Usage:
    from algorithms.Sorting import MergeSortScalingAnalyzer
    
    analyzer = MergeSortScalingAnalyzer()
    results = analyzer.run_scaling_analysis([100, 200, 500, 1000])
"""

from .merge_sort_analyzer import MergeSortScalingAnalyzer

__all__ = [
    'MergeSortScalingAnalyzer'
]
